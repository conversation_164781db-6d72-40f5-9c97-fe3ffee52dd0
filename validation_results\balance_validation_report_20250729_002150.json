{"validation_timestamp": "2025-07-29T00:21:50.051813", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6739393939393938, "confidence_interval_95": [0.5681818181818182, 0.7727272727272728], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.05669177858748396, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5606060606060607, "std_accuracy": 0.06516913081092898, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6136363636363636, "std_accuracy": 0.03214121732666122, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5984848484848485, "std_accuracy": 0.021427478217774146, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.07499617376220956, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5909090909090909, "std_accuracy": 0.0981930408849676, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6742424242424242, "std_accuracy": 0.08367697740293377, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.674) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.7141666666666667, "confidence_interval_95": [0.65, 0.775], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6333333333333333, "std_accuracy": 0.011785113019775802, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.7166666666666667, "std_accuracy": 0.05137011669140813, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.011785113019775804, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5583333333333333, "std_accuracy": 0.09428090415820634, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.55, "std_accuracy": 0.04082482904638629, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6333333333333333, "std_accuracy": 0.1312334645668635, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5583333333333332, "std_accuracy": 0.1504622507105649, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.714) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6113636363636364, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6739393939393938}, "Clay_Set2_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.7141666666666667}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.8/0.2 (accuracy: 0.674)", "  • Clay_Set2_Mid: 0.3/0.7 (accuracy: 0.714)"]}