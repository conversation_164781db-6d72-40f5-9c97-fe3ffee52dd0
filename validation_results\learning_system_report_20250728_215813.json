{"export_timestamp": "2025-07-28T21:58:13.169469", "system_version": "LearningSystemIntegration_V1", "learning_report": {"export_timestamp": "2025-07-28T21:58:13.064965", "system_version": "EnhancedAdaptiveLearningSystemV2", "configuration": {"tournament_config": {"atp_min_samples": 150, "challenger_min_samples": 120, "wta_min_samples": 140}, "validation_config": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true}}, "learning_status": {"segments": {"ATP_Clay": {"total_predictions": 605, "completed_predictions": 605, "min_required_for_learning": 260, "ready_for_learning": true, "has_learned_weights": false, "last_learning_count": 460}}, "overall_stats": {"total_segments": 1, "segments_with_learned_weights": 0, "total_predictions": 605, "total_all_predictions": 1217, "last_learning_update": "2025-07-28T17:31:32.095437"}}, "segment_weights": {}, "recent_validations": [{"timestamp": "2025-07-28T07:53:39.554019", "segment_key": "ATP_Clay", "result": {"validation_timestamp": "2025-07-28T07:53:39.552014", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "separate_by_tournament": true, "separate_by_surface": true}, "segment_results": {}, "overall_summary": {"total_segments_tested": 0, "statistically_significant_segments": 0, "overall_accuracy": 0.0, "overall_std": 0.0, "system_reliability_score": 0.0}, "recommendations": ["CRITICAL: Less than 30% of segments show statistical significance. System is not reliable.", "ACCURACY: Overall accuracy is too low for profitable betting."]}}, {"timestamp": "2025-07-28T17:31:32.096438", "segment_key": "ATP_Clay", "result": {"validation_timestamp": "2025-07-28T17:31:32.094433", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "separate_by_tournament": true, "separate_by_surface": true}, "segment_results": {"ATP_Clay": {"cross_validation": {"average_accuracy": 0.5361842105263158, "std_accuracy": 0.08696910570001504, "total_folds": 4}, "bootstrap_validation": {"mean_accuracy": 0.534671052631579, "std_accuracy": 0.04413517623351508, "confidence_interval_95": [0.45394736842105265, 0.6217105263157894], "is_statistically_significant": false, "p_value": 0.21699999999999997}, "recommendation": "NOT_RELIABLE - Results not statistically significant"}}, "overall_summary": {"total_segments_tested": 1, "statistically_significant_segments": 0, "overall_accuracy": 0.5361842105263158, "overall_std": 0.08696910570001504, "system_reliability_score": 0.0}, "recommendations": ["CRITICAL: Less than 30% of segments show statistical significance. System is not reliable.", "ACCURACY: Marginal accuracy - proceed with caution and small stakes.", "AVOID: These segments show poor performance: ATP_Clay"]}}], "recent_learning_log": [{"timestamp": "2025-07-28T07:53:39.553007", "segment_key": "ATP_Clay", "prediction_count": 260, "validation_result": {"validation_timestamp": "2025-07-28T07:53:39.552014", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "separate_by_tournament": true, "separate_by_surface": true}, "segment_results": {}, "overall_summary": {"total_segments_tested": 0, "statistically_significant_segments": 0, "overall_accuracy": 0.0, "overall_std": 0.0, "system_reliability_score": 0.0}, "recommendations": ["CRITICAL: Less than 30% of segments show statistical significance. System is not reliable.", "ACCURACY: Overall accuracy is too low for profitable betting."]}, "action_taken": "validation_failed"}, {"timestamp": "2025-07-28T17:31:32.095437", "segment_key": "ATP_Clay", "prediction_count": 460, "validation_result": {"validation_timestamp": "2025-07-28T17:31:32.094433", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "separate_by_tournament": true, "separate_by_surface": true}, "segment_results": {"ATP_Clay": {"cross_validation": {"average_accuracy": 0.5361842105263158, "std_accuracy": 0.08696910570001504, "total_folds": 4}, "bootstrap_validation": {"mean_accuracy": 0.534671052631579, "std_accuracy": 0.04413517623351508, "confidence_interval_95": [0.45394736842105265, 0.6217105263157894], "is_statistically_significant": false, "p_value": 0.21699999999999997}, "recommendation": "NOT_RELIABLE - Results not statistically significant"}}, "overall_summary": {"total_segments_tested": 1, "statistically_significant_segments": 0, "overall_accuracy": 0.5361842105263158, "overall_std": 0.08696910570001504, "system_reliability_score": 0.0}, "recommendations": ["CRITICAL: Less than 30% of segments show statistical significance. System is not reliable.", "ACCURACY: Marginal accuracy - proceed with caution and small stakes.", "AVOID: These segments show poor performance: ATP_Clay"]}, "action_taken": "validation_failed"}]}, "validation_report": {"validation_result": {"validation_timestamp": "2025-07-28T21:58:13.167469", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "separate_by_tournament": true, "separate_by_surface": true}, "segment_results": {"ATP_Clay": {"cross_validation": {"average_accuracy": 0.5539999999999999, "std_accuracy": 0.05238320341483517, "total_folds": 5}, "bootstrap_validation": {"mean_accuracy": 0.5536760000000001, "std_accuracy": 0.023366279635406224, "confidence_interval_95": [0.51, 0.598], "is_statistically_significant": true, "p_value": 0.0050000000000000044}, "recommendation": "GOOD - Moderate accuracy with statistical significance"}}, "overall_summary": {"total_segments_tested": 1, "statistically_significant_segments": 1, "overall_accuracy": 0.5539999999999999, "overall_std": 0.05238320341483517, "system_reliability_score": 1.0}, "recommendations": ["GOOD: System shows good reliability across segments.", "ACCURACY: Good accuracy levels detected."]}, "integration_recommendations": ["GOOD: Learning system shows good reliability."], "timestamp": "2025-07-28T21:58:13.169469"}, "integration_status": {"learning_system_status": {"segments": {"ATP_Clay": {"total_predictions": 605, "completed_predictions": 605, "min_required_for_learning": 260, "ready_for_learning": true, "has_learned_weights": false, "last_learning_count": 460}}, "overall_stats": {"total_segments": 1, "segments_with_learned_weights": 0, "total_predictions": 605, "total_all_predictions": 1217, "last_learning_update": "2025-07-28T17:31:32.095437"}}, "tournament_classifier_active": true, "auto_learning_active": true, "total_predictions": 618, "total_all_predictions": 1217, "segments_with_weights": 0, "last_learning_update": "2025-07-28T17:31:32.095437"}}