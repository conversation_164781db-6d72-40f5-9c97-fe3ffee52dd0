{"validation_timestamp": "2025-07-29T08:55:13.302324", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.625858244897959, "confidence_interval_95": [0.5918367346938775, 0.673469387755102], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.10135826139966898, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5238095238095238, "std_accuracy": 0.03848200169722704, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.019241000848613563, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.598639455782313, "std_accuracy": 0.08219759165710594, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6258503401360543, "std_accuracy": 0.03468720757546111, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5442176870748299, "std_accuracy": 0.10181380644282831, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.06937441515092223, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.626) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 0.6899448062015504, "confidence_interval_95": [0.627906976744186, 0.7674418604651163], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5503875968992248, "std_accuracy": 0.10457936095528717, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6201550387596899, "std_accuracy": 0.029005096021503425, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6201550387596899, "std_accuracy": 0.08562295362160664, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.689922480620155, "std_accuracy": 0.05801019204300686, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.03952728305110688, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.08973516978907149, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6434108527131782, "std_accuracy": 0.0856229536216067, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.690) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5944131805546139, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.625858244897959}, "Clay_Set2_Mid": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 0.6899448062015504}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.6/0.4 (accuracy: 0.626)", "  • Clay_Set2_Mid: 0.5/0.5 (accuracy: 0.690)"]}