{"validation_timestamp": "2025-07-29T00:52:39.600682", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 0.628761856060606, "confidence_interval_95": [0.5909090909090909, 0.6590909090909091], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5984848484848485, "std_accuracy": 0.04670010608309831, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6212121212121211, "std_accuracy": 0.05669177858748396, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5909090909090909, "std_accuracy": 0.032141217326661274, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6287878787878788, "std_accuracy": 0.028345889293741953, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5606060606060606, "std_accuracy": 0.05669177858748396, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5378787878787878, "std_accuracy": 0.10551809300897057, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.02142747821777415, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.629) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6583488083333333, "confidence_interval_95": [0.625, 0.6999999999999998], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5750000000000001, "std_accuracy": 0.04082482904638629, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5916666666666667, "std_accuracy": 0.06236095644623237, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.08498365855987972, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5916666666666667, "std_accuracy": 0.0716860438920219, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6583333333333333, "std_accuracy": 0.03118047822311616, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.625, "std_accuracy": 0.08164965809277258, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.062360956446232366, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.658) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5976731601731602, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 0.628761856060606}, "Clay_Set2_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6583488083333333}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.5/0.5 (accuracy: 0.629)", "  • Clay_Set2_Mid: 0.6/0.4 (accuracy: 0.658)"]}