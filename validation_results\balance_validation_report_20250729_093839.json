{"validation_timestamp": "2025-07-29T09:38:39.667627", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.7158506648524566, "momentum_ratio": 0.28414933514754337, "accuracy": 0.6031774716553288, "confidence_interval_95": [0.5918367346938775, 0.6122448979591837], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5260770975056689, "std_accuracy": 0.013978263045281141, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.5419501133786848, "std_accuracy": 0.013978263045281188, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.5895691609977325, "std_accuracy": 0.03348032439962226, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.013978263045281143, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.5873015873015873, "std_accuracy": 0.016968967740471408, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5895691609977325, "std_accuracy": 0.016034167373844686, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.033937935480942774, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.582766439909297, "std_accuracy": 0.022447834323382397, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.5873015873015874, "std_accuracy": 0.025046170107000575, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5419501133786849, "std_accuracy": 0.008484483870235704, "total_folds": 3}, "0.186_0.814": {"average_accuracy": 0.5941043083900227, "std_accuracy": 0.006413666949537827, "total_folds": 3}, "0.716_0.284": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.008484483870235694, "total_folds": 3}, "0.706_0.294": {"average_accuracy": 0.5555555555555555, "std_accuracy": 0.025654667798151384, "total_folds": 3}, "0.199_0.801": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.05554398509712419, "total_folds": 3}, "0.445_0.555": {"average_accuracy": 0.5600907029478458, "std_accuracy": 0.019506406501230422, "total_folds": 3}, "0.166_0.834": {"average_accuracy": 0.5804988662131519, "std_accuracy": 0.051609100574789346, "total_folds": 3}, "0.353_0.647": {"average_accuracy": 0.582766439909297, "std_accuracy": 0.016034167373844607, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5759637188208617, "std_accuracy": 0.008484483870235744, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5328798185941043, "std_accuracy": 0.01696896774047137, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.603) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.4673991135726938, "momentum_ratio": 0.5326008864273062, "accuracy": 0.6486080025839795, "confidence_interval_95": [0.6201550387596899, 0.6821705426356589], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5555555555555555, "std_accuracy": 0.02222823066419283, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6175710594315246, "std_accuracy": 0.02991172326302379, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.58656330749354, "std_accuracy": 0.009668365340501158, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5607235142118863, "std_accuracy": 0.015928718353925033, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.5839793281653747, "std_accuracy": 0.02854098454053561, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5529715762273902, "std_accuracy": 0.025580090275482338, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.648578811369509, "std_accuracy": 0.025580090275482338, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5607235142118863, "std_accuracy": 0.03248011651156728, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.5710594315245479, "std_accuracy": 0.0263515220340712, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5684754521963824, "std_accuracy": 0.020346273576257946, "total_folds": 3}, "0.472_0.528": {"average_accuracy": 0.5219638242894057, "std_accuracy": 0.015928718353925026, "total_folds": 3}, "0.255_0.745": {"average_accuracy": 0.5555555555555555, "std_accuracy": 0.0073085972215663954, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5116279069767442, "std_accuracy": 0.029005096021503425, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5917312661498708, "std_accuracy": 0.025580090275482335, "total_folds": 3}, "0.237_0.763": {"average_accuracy": 0.6098191214470284, "std_accuracy": 0.009668365340501157, "total_folds": 3}, "0.230_0.770": {"average_accuracy": 0.571059431524548, "std_accuracy": 0.050767138771029764, "total_folds": 3}, "0.111_0.889": {"average_accuracy": 0.5839793281653747, "std_accuracy": 0.020346273576257946, "total_folds": 3}, "0.442_0.558": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.010962895832349542, "total_folds": 3}, "0.431_0.569": {"average_accuracy": 0.5762273901808787, "std_accuracy": 0.0348597869850957, "total_folds": 3}, "0.125_0.875": {"average_accuracy": 0.640826873385013, "std_accuracy": 0.03815199757166251, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.649) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5754972233377551, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.7158506648524566, "momentum_ratio": 0.28414933514754337, "accuracy": 0.6031774716553288}, "Clay_Set2_Mid": {"historical_ratio": 0.4673991135726938, "momentum_ratio": 0.5326008864273062, "accuracy": 0.6486080025839795}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Marginal accuracy improvement from balance optimization. Consider other factors.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.7/0.3 (accuracy: 0.603)", "  • Clay_Set2_Mid: 0.5/0.5 (accuracy: 0.649)"]}