{"validation_timestamp": "2025-07-29T08:27:27.384281", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 0.639446462585034, "confidence_interval_95": [0.5918367346938775, 0.6938775510204082], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5374149659863945, "std_accuracy": 0.019241000848613507, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.06308584010541295, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6326530612244898, "std_accuracy": 0.04998958658741181, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6394557823129251, "std_accuracy": 0.04193478913584338, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.06008000589338669, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.025453451610707112, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.044086671417740565, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.639) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6201713875968994, "confidence_interval_95": [0.5348837209302325, 0.7441860465116279], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.010962895832349542, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6201550387596899, "std_accuracy": 0.08973516978907153, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.05696487773914369, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.050238299987657865, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.06846326252967325, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.029005096021503435, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5891472868217055, "std_accuracy": 0.054814479161747864, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.620) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5958935069044229, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 0.639446462585034}, "Clay_Set2_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6201713875968994}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.5/0.5 (accuracy: 0.639)", "  • Clay_Set2_Mid: 0.3/0.7 (accuracy: 0.620)"]}