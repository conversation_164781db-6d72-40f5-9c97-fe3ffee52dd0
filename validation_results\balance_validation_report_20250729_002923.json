{"validation_timestamp": "2025-07-29T00:29:23.114007", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.7197422045454543, "confidence_interval_95": [0.6363636363636364, 0.7727272727272728], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6363636363636364, "std_accuracy": 0.03214121732666122, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.7196969696969697, "std_accuracy": 0.059651574803119785, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6136363636363636, "std_accuracy": 0.06428243465332249, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5757575757575758, "std_accuracy": 0.0214274782177742, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.059651574803119785, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5757575757575758, "std_accuracy": 0.09153822707268618, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5984848484848485, "std_accuracy": 0.05356869554443543, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.720) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.691637733333333, "confidence_interval_95": [0.575, 0.775], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5583333333333332, "std_accuracy": 0.05892556509887896, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5499999999999999, "std_accuracy": 0.05400617248673217, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6916666666666668, "std_accuracy": 0.08498365855987977, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6583333333333333, "std_accuracy": 0.04249182927993988, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6083333333333334, "std_accuracy": 0.011785113019775804, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5416666666666666, "std_accuracy": 0.09646530752325189, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6416666666666667, "std_accuracy": 0.08249579113843053, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.692) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6136363636363636, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.7197422045454543}, "Clay_Set2_Mid": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.691637733333333}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.3/0.7 (accuracy: 0.720)", "  • Clay_Set2_Mid: 0.4/0.6 (accuracy: 0.692)"]}