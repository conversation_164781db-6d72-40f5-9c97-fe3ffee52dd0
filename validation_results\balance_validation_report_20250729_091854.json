{"validation_timestamp": "2025-07-29T09:18:54.097567", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.6207107783590824, "momentum_ratio": 0.3792892216409176, "accuracy": 0.6575805646258505, "confidence_interval_95": [0.6326530612244898, 0.7006802721088435], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.6303854875283447, "std_accuracy": 0.016968967740471408, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6235827664399093, "std_accuracy": 0.06092983602060947, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.6077097505668935, "std_accuracy": 0.02795652609056224, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.6031746031746033, "std_accuracy": 0.013978263045281094, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.6145124716553289, "std_accuracy": 0.07581974375192965, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.6213151927437642, "std_accuracy": 0.017854893138348798, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5691609977324262, "std_accuracy": 0.008484483870235713, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.6077097505668935, "std_accuracy": 0.053947289130931116, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.03888078956798691, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.6575963718820862, "std_accuracy": 0.030591241639981958, "total_folds": 3}, "0.625_0.375": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.025453451610707015, "total_folds": 3}, "0.858_0.142": {"average_accuracy": 0.6009070294784581, "std_accuracy": 0.01156240252515372, "total_folds": 3}, "0.611_0.389": {"average_accuracy": 0.582766439909297, "std_accuracy": 0.01603416737384463, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.005554398509712428, "total_folds": 3}, "0.748_0.252": {"average_accuracy": 0.6122448979591837, "std_accuracy": 0.02777199254856214, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5986394557823129, "std_accuracy": 0.0364226267669138, "total_folds": 3}, "0.889_0.111": {"average_accuracy": 0.6167800453514739, "std_accuracy": 0.01156240252515372, "total_folds": 3}, "0.260_0.740": {"average_accuracy": 0.6077097505668934, "std_accuracy": 0.033937935480942726, "total_folds": 3}, "0.235_0.765": {"average_accuracy": 0.6009070294784581, "std_accuracy": 0.039012813002460886, "total_folds": 3}, "0.166_0.834": {"average_accuracy": 0.6077097505668935, "std_accuracy": 0.01156240252515372, "total_folds": 3}, "0.193_0.807": {"average_accuracy": 0.6122448979591836, "std_accuracy": 0.020026668631128914, "total_folds": 3}, "0.765_0.235": {"average_accuracy": 0.6462585034013605, "std_accuracy": 0.009620500424306832, "total_folds": 3}, "0.772_0.228": {"average_accuracy": 0.6009070294784581, "std_accuracy": 0.08055473772485992, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.658) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.738533386303331, "momentum_ratio": 0.261466613696669, "accuracy": 0.6537434909560725, "confidence_interval_95": [0.6201550387596899, 0.6744186046511628], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5348837209302325, "std_accuracy": 0.045642175019782134, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6330749354005168, "std_accuracy": 0.0036542986107831977, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.05063544687923883, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.6175710594315246, "std_accuracy": 0.0886873503965401, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.6098191214470284, "std_accuracy": 0.043083545221015665, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.027589349488452947, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5607235142118864, "std_accuracy": 0.023962838489652975, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.6020671834625323, "std_accuracy": 0.036542986107831923, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.6408268733850129, "std_accuracy": 0.05270304406814249, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.6227390180878554, "std_accuracy": 0.03708708034730571, "total_folds": 3}, "0.220_0.780": {"average_accuracy": 0.5943152454780362, "std_accuracy": 0.03122234101704024, "total_folds": 3}, "0.744_0.256": {"average_accuracy": 0.648578811369509, "std_accuracy": 0.0036542986107831977, "total_folds": 3}, "0.747_0.253": {"average_accuracy": 0.6098191214470284, "std_accuracy": 0.05383117998966321, "total_folds": 3}, "0.739_0.261": {"average_accuracy": 0.6537467700258398, "std_accuracy": 0.023962838489652975, "total_folds": 3}, "0.204_0.796": {"average_accuracy": 0.5917312661498708, "std_accuracy": 0.029911723263023823, "total_folds": 3}, "0.257_0.743": {"average_accuracy": 0.6459948320413437, "std_accuracy": 0.05672997002609857, "total_folds": 3}, "0.264_0.736": {"average_accuracy": 0.648578811369509, "std_accuracy": 0.02635152203407125, "total_folds": 3}, "0.270_0.730": {"average_accuracy": 0.6356589147286821, "std_accuracy": 0.033492199991771845, "total_folds": 3}, "0.261_0.739": {"average_accuracy": 0.5710594315245477, "std_accuracy": 0.01592871835392499, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.58656330749354, "std_accuracy": 0.06339971135082838, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5581395348837209, "std_accuracy": 0.04150485375764601, "total_folds": 3}, "0.306_0.694": {"average_accuracy": 0.6124031007751938, "std_accuracy": 0.02192579166469911, "total_folds": 3}, "0.811_0.189": {"average_accuracy": 0.6020671834625323, "std_accuracy": 0.03185743670784997, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.654) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6079161020660371, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.6207107783590824, "momentum_ratio": 0.3792892216409176, "accuracy": 0.6575805646258505}, "Clay_Set2_Mid": {"historical_ratio": 0.738533386303331, "momentum_ratio": 0.261466613696669, "accuracy": 0.6537434909560725}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.6/0.4 (accuracy: 0.658)", "  • Clay_Set2_Mid: 0.7/0.3 (accuracy: 0.654)"]}