{"validation_timestamp": "2025-07-29T08:26:13.270195", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.2, "momentum_ratio": 0.8, "accuracy": 0.639485619047619, "confidence_interval_95": [0.5714285714285714, 0.6938775510204082], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6394557823129251, "std_accuracy": 0.050906903221414175, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6190476190476191, "std_accuracy": 0.07874718981489948, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.03848200169722707, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.050906903221414175, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.04193478913584338, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5714285714285714, "std_accuracy": 0.057723002545840625, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5170068027210885, "std_accuracy": 0.03848200169722706, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.639) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6279348294573643, "confidence_interval_95": [0.5581395348837209, 0.6744186046511628], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6124031007751938, "std_accuracy": 0.029005096021503435, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5581395348837209, "std_accuracy": 0.05696487773914369, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.12207764145754746, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6124031007751938, "std_accuracy": 0.08562295362160664, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.03797658515942914, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5658914728682171, "std_accuracy": 0.09557231012355001, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.627906976744186, "std_accuracy": 0.05023829998765784, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.628) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5886161773679572, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.2, "momentum_ratio": 0.8, "accuracy": 0.639485619047619}, "Clay_Set2_Mid": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6279348294573643}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.2/0.8 (accuracy: 0.639)", "  • Clay_Set2_Mid: 0.8/0.2 (accuracy: 0.628)"]}