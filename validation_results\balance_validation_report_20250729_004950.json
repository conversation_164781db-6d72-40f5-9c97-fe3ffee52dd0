{"validation_timestamp": "2025-07-29T00:49:50.664005", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6287927045454546, "confidence_interval_95": [0.5909090909090909, 0.6590909090909091], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5606060606060606, "std_accuracy": 0.03862893570903626, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5681818181818182, "std_accuracy": 0.08088695645478267, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5757575757575757, "std_accuracy": 0.0214274782177742, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5303030303030304, "std_accuracy": 0.03862893570903625, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6287878787878788, "std_accuracy": 0.028345889293741953, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.038628935709036215, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.553030303030303, "std_accuracy": 0.059651574803119785, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.629) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6416479250000001, "confidence_interval_95": [0.575, 0.6999999999999998], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.06561673228343176, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6416666666666666, "std_accuracy": 0.04249182927993986, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5666666666666668, "std_accuracy": 0.05892556509887896, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5416666666666666, "std_accuracy": 0.03118047822311617, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6416666666666667, "std_accuracy": 0.051370116691408146, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.625, "std_accuracy": 0.02041241452319317, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5416666666666666, "std_accuracy": 0.04714045207910316, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.642) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5842532467532467, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6287927045454546}, "Clay_Set2_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6416479250000001}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.6/0.4 (accuracy: 0.629)", "  • Clay_Set2_Mid: 0.6/0.4 (accuracy: 0.642)"]}