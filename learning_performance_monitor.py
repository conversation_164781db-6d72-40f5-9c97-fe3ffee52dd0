"""
Learning Performance Monitor
Tracks accuracy trends and system changes to identify causes of performance drops
"""

import json
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import numpy as np

class LearningPerformanceMonitor:
    """
    Monitors learning system performance and identifies causes of accuracy drops
    """
    
    def __init__(self):
        self.monitor_file = Path("learning_performance_monitor.json")
        self.events_log: List[Dict[str, Any]] = []
        self.accuracy_history: List[Dict[str, Any]] = []
        self.system_changes: List[Dict[str, Any]] = []
        
        self.load_data()
    
    def log_accuracy_measurement(self, accuracy: float, sample_size: int, 
                                context: str = "overall", metadata: Dict[str, Any] = None):
        """Log an accuracy measurement"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'accuracy': accuracy,
            'sample_size': sample_size,
            'context': context,
            'metadata': metadata or {}
        }
        
        self.accuracy_history.append(entry)
        self._detect_accuracy_drops()
        self.save_data()
    
    def log_system_change(self, system_name: str, change_type: str, 
                         details: Dict[str, Any], triggered_by: str = "automatic"):
        """Log a system change (balance update, weight validation, etc.)"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'system_name': system_name,
            'change_type': change_type,
            'details': details,
            'triggered_by': triggered_by
        }
        
        self.system_changes.append(entry)
        self.save_data()
    
    def log_event(self, event_type: str, description: str, 
                  severity: str = "info", metadata: Dict[str, Any] = None):
        """Log a general event"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'description': description,
            'severity': severity,
            'metadata': metadata or {}
        }
        
        self.events_log.append(entry)
        self.save_data()
    
    def _detect_accuracy_drops(self):
        """Detect significant accuracy drops and log them"""
        if len(self.accuracy_history) < 10:
            return
        
        # Get recent accuracy measurements
        recent_10 = self.accuracy_history[-10:]
        recent_avg = sum(entry['accuracy'] for entry in recent_10) / len(recent_10)
        
        if len(self.accuracy_history) >= 20:
            older_10 = self.accuracy_history[-20:-10]
            older_avg = sum(entry['accuracy'] for entry in older_10) / len(older_10)
            
            drop = older_avg - recent_avg
            
            if drop > 0.05:  # 5% drop
                self.log_event(
                    event_type="accuracy_drop_detected",
                    description=f"Accuracy dropped by {drop:.3f} ({older_avg:.3f} -> {recent_avg:.3f})",
                    severity="warning",
                    metadata={
                        'drop_amount': drop,
                        'older_accuracy': older_avg,
                        'recent_accuracy': recent_avg,
                        'recent_changes': self._get_recent_system_changes(hours=24)
                    }
                )
    
    def _get_recent_system_changes(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get system changes from the last N hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        recent_changes = []
        for change in self.system_changes:
            change_time = datetime.fromisoformat(change['timestamp'])
            if change_time > cutoff:
                recent_changes.append(change)
        
        return recent_changes
    
    def analyze_accuracy_trends(self, days: int = 7) -> Dict[str, Any]:
        """Analyze accuracy trends over the last N days"""
        cutoff = datetime.now() - timedelta(days=days)
        
        recent_accuracy = []
        for entry in self.accuracy_history:
            entry_time = datetime.fromisoformat(entry['timestamp'])
            if entry_time > cutoff:
                recent_accuracy.append(entry)
        
        if len(recent_accuracy) < 5:
            return {'status': 'insufficient_data', 'entries': len(recent_accuracy)}
        
        # Calculate trend
        accuracies = [entry['accuracy'] for entry in recent_accuracy]
        timestamps = [datetime.fromisoformat(entry['timestamp']) for entry in recent_accuracy]
        
        # Simple linear trend
        x = np.arange(len(accuracies))
        trend_slope = np.polyfit(x, accuracies, 1)[0]
        
        # Volatility (standard deviation)
        volatility = np.std(accuracies)
        
        # Recent vs older comparison
        mid_point = len(accuracies) // 2
        recent_half = accuracies[mid_point:]
        older_half = accuracies[:mid_point]
        
        recent_avg = np.mean(recent_half)
        older_avg = np.mean(older_half) if older_half else recent_avg
        
        return {
            'status': 'analysis_complete',
            'period_days': days,
            'total_measurements': len(recent_accuracy),
            'trend_slope': trend_slope,
            'volatility': volatility,
            'recent_average': recent_avg,
            'older_average': older_avg,
            'change': recent_avg - older_avg,
            'min_accuracy': min(accuracies),
            'max_accuracy': max(accuracies),
            'current_accuracy': accuracies[-1] if accuracies else 0,
            'trend_direction': 'improving' if trend_slope > 0.001 else 'declining' if trend_slope < -0.001 else 'stable'
        }
    
    def correlate_changes_with_performance(self, days: int = 14) -> Dict[str, Any]:
        """Correlate system changes with performance changes"""
        cutoff = datetime.now() - timedelta(days=days)
        
        # Get recent changes and accuracy measurements
        recent_changes = self._get_recent_system_changes(hours=days*24)
        recent_accuracy = [entry for entry in self.accuracy_history 
                          if datetime.fromisoformat(entry['timestamp']) > cutoff]
        
        correlations = []
        
        for change in recent_changes:
            change_time = datetime.fromisoformat(change['timestamp'])
            
            # Get accuracy before and after the change
            before_accuracy = []
            after_accuracy = []
            
            for acc_entry in recent_accuracy:
                acc_time = datetime.fromisoformat(acc_entry['timestamp'])
                time_diff = (acc_time - change_time).total_seconds() / 3600  # hours
                
                if -24 <= time_diff <= 0:  # 24 hours before
                    before_accuracy.append(acc_entry['accuracy'])
                elif 0 < time_diff <= 24:  # 24 hours after
                    after_accuracy.append(acc_entry['accuracy'])
            
            if before_accuracy and after_accuracy:
                before_avg = np.mean(before_accuracy)
                after_avg = np.mean(after_accuracy)
                impact = after_avg - before_avg
                
                correlations.append({
                    'change': change,
                    'before_accuracy': before_avg,
                    'after_accuracy': after_avg,
                    'impact': impact,
                    'impact_magnitude': abs(impact),
                    'impact_direction': 'positive' if impact > 0.01 else 'negative' if impact < -0.01 else 'neutral'
                })
        
        # Sort by impact magnitude
        correlations.sort(key=lambda x: x['impact_magnitude'], reverse=True)
        
        return {
            'analysis_period_days': days,
            'total_changes_analyzed': len(recent_changes),
            'correlations_found': len(correlations),
            'correlations': correlations[:10],  # Top 10 most impactful
            'summary': self._summarize_correlations(correlations)
        }
    
    def _summarize_correlations(self, correlations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize correlation findings"""
        if not correlations:
            return {'status': 'no_correlations_found'}
        
        positive_impacts = [c for c in correlations if c['impact'] > 0.01]
        negative_impacts = [c for c in correlations if c['impact'] < -0.01]
        
        # Find most problematic change types
        negative_by_type = {}
        for corr in negative_impacts:
            change_type = corr['change']['change_type']
            if change_type not in negative_by_type:
                negative_by_type[change_type] = []
            negative_by_type[change_type].append(corr['impact'])
        
        problematic_types = []
        for change_type, impacts in negative_by_type.items():
            avg_impact = np.mean(impacts)
            problematic_types.append({
                'change_type': change_type,
                'average_negative_impact': avg_impact,
                'occurrences': len(impacts)
            })
        
        problematic_types.sort(key=lambda x: x['average_negative_impact'])
        
        return {
            'total_positive_impacts': len(positive_impacts),
            'total_negative_impacts': len(negative_impacts),
            'most_problematic_change_types': problematic_types[:5],
            'largest_negative_impact': min([c['impact'] for c in correlations]) if correlations else 0,
            'largest_positive_impact': max([c['impact'] for c in correlations]) if correlations else 0
        }
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        return {
            'report_timestamp': datetime.now().isoformat(),
            'accuracy_trends': self.analyze_accuracy_trends(days=7),
            'change_correlations': self.correlate_changes_with_performance(days=14),
            'recent_events': [event for event in self.events_log[-20:] if event['severity'] in ['warning', 'error']],
            'system_health': self._assess_system_health(),
            'recommendations': self._generate_recommendations()
        }
    
    def _assess_system_health(self) -> Dict[str, Any]:
        """Assess overall system health"""
        trends = self.analyze_accuracy_trends(days=7)
        
        if trends['status'] != 'analysis_complete':
            return {'status': 'insufficient_data'}
        
        health_score = 100
        issues = []
        
        # Check accuracy trend
        if trends['trend_direction'] == 'declining':
            health_score -= 30
            issues.append(f"Declining accuracy trend (slope: {trends['trend_slope']:.4f})")
        
        # Check volatility
        if trends['volatility'] > 0.1:
            health_score -= 20
            issues.append(f"High accuracy volatility ({trends['volatility']:.3f})")
        
        # Check recent accuracy drop
        if trends['change'] < -0.05:
            health_score -= 25
            issues.append(f"Recent accuracy drop ({trends['change']:.3f})")
        
        # Check for recent errors
        recent_errors = [event for event in self.events_log[-50:] if event['severity'] == 'error']
        if len(recent_errors) > 5:
            health_score -= 15
            issues.append(f"Multiple recent errors ({len(recent_errors)})")
        
        health_score = max(0, health_score)
        
        if health_score >= 80:
            status = 'healthy'
        elif health_score >= 60:
            status = 'warning'
        else:
            status = 'critical'
        
        return {
            'status': status,
            'health_score': health_score,
            'issues': issues,
            'current_accuracy': trends['current_accuracy']
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        health = self._assess_system_health()
        trends = self.analyze_accuracy_trends(days=7)
        correlations = self.correlate_changes_with_performance(days=14)
        
        if health['status'] == 'critical':
            recommendations.append("🚨 CRITICAL: Consider reverting recent system changes")
            recommendations.append("🛡️ Enable accuracy drop protection mode")
        
        if trends.get('trend_direction') == 'declining':
            recommendations.append("📉 Investigate causes of declining accuracy trend")
            recommendations.append("🔍 Review recent balance and weight changes")
        
        if trends.get('volatility', 0) > 0.1:
            recommendations.append("📊 High volatility detected - consider more conservative thresholds")
        
        # Check for problematic change types
        problematic = correlations.get('summary', {}).get('most_problematic_change_types', [])
        if problematic:
            worst_type = problematic[0]['change_type']
            recommendations.append(f"⚠️ '{worst_type}' changes show negative correlation with accuracy")
        
        if not recommendations:
            recommendations.append("✅ System appears healthy - continue monitoring")
        
        return recommendations
    
    def save_data(self):
        """Save monitoring data to file"""
        data = {
            'events_log': self.events_log[-1000:],  # Keep last 1000 events
            'accuracy_history': self.accuracy_history[-500:],  # Keep last 500 measurements
            'system_changes': self.system_changes[-200:]  # Keep last 200 changes
        }
        
        with open(self.monitor_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_data(self):
        """Load monitoring data from file"""
        if not self.monitor_file.exists():
            return
        
        try:
            with open(self.monitor_file, 'r') as f:
                data = json.load(f)
            
            self.events_log = data.get('events_log', [])
            self.accuracy_history = data.get('accuracy_history', [])
            self.system_changes = data.get('system_changes', [])
        
        except Exception as e:
            print(f"⚠️ Could not load monitoring data: {e}")

# Global monitor instance
performance_monitor = LearningPerformanceMonitor()
