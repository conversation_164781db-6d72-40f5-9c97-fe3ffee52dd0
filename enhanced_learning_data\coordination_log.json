[{"operation_type": "balance_validation", "priority": 1, "requested_at": "2025-07-29T01:21:06.996253", "system_name": "enhanced_learning_balance_system", "estimated_duration_minutes": 3, "metadata": {"completed_at": "2025-07-29T01:22:20.933592", "success": false, "results": {"status": "failed", "validation_type": "balance_validation", "validation_result": {"validation_timestamp": "2025-07-29T01:22:20.909582", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6590908181818182, "confidence_interval_95": [0.5681818181818182, 0.75], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.13927860841553213, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6590909090909091, "std_accuracy": 0.07422696190252052, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.07499617376220957, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5303030303030303, "std_accuracy": 0.010713739108887075, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6439393939393939, "std_accuracy": 0.038628935709036215, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5909090909090909, "std_accuracy": 0.11134044285378084, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5454545454545454, "std_accuracy": 0.08503766788122594, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.659) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6333103666666671, "confidence_interval_95": [0.6, 0.65], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5416666666666666, "std_accuracy": 0.07728015412913089, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.575, "std_accuracy": 0.1136515141415488, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6333333333333333, "std_accuracy": 0.023570226039551608, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5666666666666667, "std_accuracy": 0.0716860438920219, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6, "std_accuracy": 0.04082482904638629, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.05137011669140814, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6166666666666667, "std_accuracy": 0.11242281302693369, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.633) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.59491341991342, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6590908181818182}, "Clay_Set2_Mid": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6333103666666671}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.3/0.7 (accuracy: 0.659)", "  • Clay_Set2_Mid: 0.4/0.6 (accuracy: 0.633)"]}}}}, {"operation_type": "weight_validation", "priority": 2, "requested_at": "2025-07-29T01:22:21.075351", "system_name": "enhanced_learning_weight_system", "estimated_duration_minutes": 5, "metadata": {"completed_at": "2025-07-29T01:22:21.118340", "success": false, "results": {"status": "no_improvement", "validation_type": "weight_validation", "optimization_result": {"status": "no_significant_improvement", "current_accuracy": 0.5896296296296296, "predicted_improvement": 0.04682026379289772, "confidence": 0.9847313796741584}}}}, {"operation_type": "balance_validation", "priority": 1, "requested_at": "2025-07-29T08:17:10.625330", "system_name": "enhanced_learning_balance_system", "estimated_duration_minutes": 3, "metadata": {"completed_at": "2025-07-29T08:18:00.669532", "success": false, "results": {"status": "failed", "validation_type": "balance_validation", "validation_result": {"validation_timestamp": "2025-07-29T08:18:00.650519", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.673508142857143, "confidence_interval_95": [0.6122448979591837, 0.7959183673469387], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6394557823129251, "std_accuracy": 0.05356467941504634, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.025453451610707112, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.673469387755102, "std_accuracy": 0.08658450381876091, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.1261716802108259, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6122448979591836, "std_accuracy": 0.04998958658741181, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.025453451610707112, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.03468720757546111, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.674) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.720930232558141, "confidence_interval_95": [0.7209302325581395, 0.7209302325581395], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6356589147286821, "std_accuracy": 0.047786155061775006, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.7209302325581395, "std_accuracy": 0.0, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5503875968992248, "std_accuracy": 0.029005096021503425, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.04385158332939827, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5503875968992248, "std_accuracy": 0.10457936095528717, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.018988292579714548, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5503875968992249, "std_accuracy": 0.04385158332939832, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.721) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6052613736524509, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.673508142857143}, "Clay_Set2_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.720930232558141}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.4/0.6 (accuracy: 0.674)", "  • Clay_Set2_Mid: 0.3/0.7 (accuracy: 0.721)"]}}}}]