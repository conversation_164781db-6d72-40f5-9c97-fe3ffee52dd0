{"validation_timestamp": "2025-07-29T01:44:09.632519", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6666666666666669, "confidence_interval_95": [0.6666666666666666, 0.6666666666666666], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 135}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6148148148148148, "std_accuracy": 0.05832598425193934, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6296296296296297, "std_accuracy": 0.027715980642769925, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.0, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.07554102983100422, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.055431961285539864, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5703703703703703, "std_accuracy": 0.020951312035156943, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.03142696805273544, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.667) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6500151, "confidence_interval_95": [0.6, 0.6750000000000002], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.47500000000000003, "std_accuracy": 0.020412414523193145, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6166666666666667, "std_accuracy": 0.09646530752325189, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6083333333333333, "std_accuracy": 0.051370116691408146, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6333333333333334, "std_accuracy": 0.062360956446232324, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.65, "std_accuracy": 0.03535533905932741, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5666666666666667, "std_accuracy": 0.08249579113843057, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6, "std_accuracy": 0.02041241452319317, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.650) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6027777777777777, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6666666666666669}, "Clay_Set2_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6500151}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.4/0.6 (accuracy: 0.667)", "  • Clay_Set2_Mid: 0.6/0.4 (accuracy: 0.650)"]}