{"validation_timestamp": "2025-07-29T09:34:34.771766", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.22436861602014002, "momentum_ratio": 0.77563138397986, "accuracy": 0.6235754603174603, "confidence_interval_95": [0.5918367346938775, 0.6462585034013606], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.011562402525153734, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.5850340136054423, "std_accuracy": 0.024211061795989346, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.582766439909297, "std_accuracy": 0.016968967740471408, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5941043083900227, "std_accuracy": 0.023124805050307354, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.5691609977324262, "std_accuracy": 0.044895668646764926, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.6122448979591836, "std_accuracy": 0.02421106179598929, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5215419501133787, "std_accuracy": 0.03698300777845823, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5578231292517006, "std_accuracy": 0.019241000848613507, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.040942109036888406, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.585034013605442, "std_accuracy": 0.04998958658741186, "total_folds": 3}, "0.175_0.825": {"average_accuracy": 0.619047619047619, "std_accuracy": 0.02421106179598934, "total_folds": 3}, "0.167_0.833": {"average_accuracy": 0.562358276643991, "std_accuracy": 0.011562402525153705, "total_folds": 3}, "0.186_0.814": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.041688835171996, "total_folds": 3}, "0.590_0.410": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.033937935480942774, "total_folds": 3}, "0.565_0.435": {"average_accuracy": 0.582766439909297, "std_accuracy": 0.033937935480942726, "total_folds": 3}, "0.178_0.822": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.03527516822245814, "total_folds": 3}, "0.195_0.805": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.019506406501230377, "total_folds": 3}, "0.267_0.733": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.008484483870235664, "total_folds": 3}, "0.277_0.723": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.019241000848613483, "total_folds": 3}, "0.259_0.741": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.01110879701942481, "total_folds": 3}, "0.645_0.355": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.049783443084127355, "total_folds": 3}, "0.700_0.300": {"average_accuracy": 0.5283446712018139, "std_accuracy": 0.006413666949537801, "total_folds": 3}, "0.428_0.572": {"average_accuracy": 0.5850340136054423, "std_accuracy": 0.057989622914033645, "total_folds": 3}, "0.408_0.592": {"average_accuracy": 0.5804988662131519, "std_accuracy": 0.061934241649649245, "total_folds": 3}, "0.887_0.113": {"average_accuracy": 0.54421768707483, "std_accuracy": 0.005554398509712428, "total_folds": 3}, "0.535_0.465": {"average_accuracy": 0.5600907029478458, "std_accuracy": 0.021028613368470953, "total_folds": 3}, "0.304_0.696": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.06259489225325288, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.022217594038849712, "total_folds": 3}, "0.310_0.690": {"average_accuracy": 0.5691609977324262, "std_accuracy": 0.04788823601347592, "total_folds": 3}, "0.298_0.702": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.025453451610707025, "total_folds": 3}, "0.810_0.190": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.0364226267669138, "total_folds": 3}, "0.775_0.225": {"average_accuracy": 0.5736961451247167, "std_accuracy": 0.022447834323382487, "total_folds": 3}, "0.845_0.155": {"average_accuracy": 0.546485260770975, "std_accuracy": 0.05451616907107165, "total_folds": 3}, "0.504_0.496": {"average_accuracy": 0.5646258503401361, "std_accuracy": 0.009620500424306832, "total_folds": 3}, "0.135_0.865": {"average_accuracy": 0.6167800453514739, "std_accuracy": 0.03393793548094272, "total_folds": 3}, "0.149_0.851": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.02312480505030739, "total_folds": 3}, "0.224_0.776": {"average_accuracy": 0.6235827664399093, "std_accuracy": 0.02312480505030744, "total_folds": 3}, "0.231_0.769": {"average_accuracy": 0.6009070294784581, "std_accuracy": 0.019506406501230467, "total_folds": 3}, "0.232_0.768": {"average_accuracy": 0.5691609977324262, "std_accuracy": 0.016968967740471408, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5804988662131518, "std_accuracy": 0.03158364688703882, "total_folds": 3}, "0.329_0.671": {"average_accuracy": 0.5941043083900227, "std_accuracy": 0.0578120126257685, "total_folds": 3}, "0.328_0.672": {"average_accuracy": 0.5759637188208616, "std_accuracy": 0.02739919721903526, "total_folds": 3}, "0.250_0.750": {"average_accuracy": 0.6145124716553289, "std_accuracy": 0.03901281300246089, "total_folds": 3}, "0.245_0.755": {"average_accuracy": 0.6235827664399093, "std_accuracy": 0.05990859325906078, "total_folds": 3}, "0.236_0.764": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.02739919721903529, "total_folds": 3}, "0.340_0.660": {"average_accuracy": 0.5873015873015873, "std_accuracy": 0.05781201262576851, "total_folds": 3}, "0.286_0.714": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.06185116415866604, "total_folds": 3}, "0.355_0.645": {"average_accuracy": 0.6009070294784581, "std_accuracy": 0.012827333899075706, "total_folds": 3}, "0.274_0.726": {"average_accuracy": 0.6213151927437641, "std_accuracy": 0.019506406501230464, "total_folds": 3}, "0.221_0.779": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.019241000848613535, "total_folds": 3}, "0.351_0.649": {"average_accuracy": 0.564625850340136, "std_accuracy": 0.04408667141774053, "total_folds": 3}, "0.648_0.352": {"average_accuracy": 0.5419501133786847, "std_accuracy": 0.027956526090562234, "total_folds": 3}, "0.403_0.597": {"average_accuracy": 0.5691609977324263, "std_accuracy": 0.026249063271633143, "total_folds": 3}, "0.243_0.757": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.01696896774047136, "total_folds": 3}, "0.233_0.767": {"average_accuracy": 0.5759637188208617, "std_accuracy": 0.016034167373844686, "total_folds": 3}, "0.414_0.586": {"average_accuracy": 0.546485260770975, "std_accuracy": 0.017854893138348746, "total_folds": 3}, "0.212_0.788": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.011562402525153734, "total_folds": 3}, "0.701_0.299": {"average_accuracy": 0.5759637188208616, "std_accuracy": 0.02624906327163319, "total_folds": 3}, "0.549_0.451": {"average_accuracy": 0.5963718820861678, "std_accuracy": 0.03527516822245819, "total_folds": 3}, "0.297_0.703": {"average_accuracy": 0.5963718820861678, "std_accuracy": 0.05160910057478926, "total_folds": 3}, "0.568_0.432": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.0394062294716162, "total_folds": 3}, "0.187_0.813": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.009620500424306781, "total_folds": 3}, "0.610_0.390": {"average_accuracy": 0.54421768707483, "std_accuracy": 0.06109838360683664, "total_folds": 3}, "0.295_0.705": {"average_accuracy": 0.5895691609977324, "std_accuracy": 0.03158364688703877, "total_folds": 3}, "0.765_0.235": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.02312480505030744, "total_folds": 3}, "0.198_0.802": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.006413666949537853, "total_folds": 3}, "0.497_0.503": {"average_accuracy": 0.5510204081632654, "std_accuracy": 0.03092558207933304, "total_folds": 3}, "0.293_0.707": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.03378608713322296, "total_folds": 3}, "0.287_0.713": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.01696896774047136, "total_folds": 3}, "0.190_0.810": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.028502959387701875, "total_folds": 3}, "0.774_0.226": {"average_accuracy": 0.5895691609977324, "std_accuracy": 0.03780800907150354, "total_folds": 3}, "0.797_0.203": {"average_accuracy": 0.5850340136054423, "std_accuracy": 0.025453451610707074, "total_folds": 3}, "0.160_0.840": {"average_accuracy": 0.5941043083900227, "std_accuracy": 0.04205722673694191, "total_folds": 3}, "0.278_0.722": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.0279565260905622, "total_folds": 3}, "0.673_0.327": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.04723960692970445, "total_folds": 3}, "0.359_0.641": {"average_accuracy": 0.5487528344671202, "std_accuracy": 0.030591241639981937, "total_folds": 3}, "0.801_0.199": {"average_accuracy": 0.5895691609977324, "std_accuracy": 0.03254580520273773, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.624) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.30691083600635227, "momentum_ratio": 0.6930891639936477, "accuracy": 0.6382500206718345, "confidence_interval_95": [0.6124031007751939, 0.6744186046511628], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5452196382428941, "std_accuracy": 0.013175761017035593, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.5555555555555557, "std_accuracy": 0.060157347425440946, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.5426356589147286, "std_accuracy": 0.006329430859904864, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5348837209302326, "std_accuracy": 0.010962895832349568, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.6227390180878554, "std_accuracy": 0.028540984540535518, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.038500424872742495, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.6020671834625323, "std_accuracy": 0.02854098454053552, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5839793281653747, "std_accuracy": 0.013175761017035643, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.5607235142118864, "std_accuracy": 0.01592871835392499, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.545219638242894, "std_accuracy": 0.05457031545721672, "total_folds": 3}, "0.427_0.573": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.012658861719809728, "total_folds": 3}, "0.497_0.503": {"average_accuracy": 0.5607235142118863, "std_accuracy": 0.03708708034730568, "total_folds": 3}, "0.448_0.552": {"average_accuracy": 0.6072351421188631, "std_accuracy": 0.025580090275482345, "total_folds": 3}, "0.326_0.674": {"average_accuracy": 0.5710594315245477, "std_accuracy": 0.04490477311881856, "total_folds": 3}, "0.122_0.878": {"average_accuracy": 0.5684754521963824, "std_accuracy": 0.020346273576257946, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5322997416020673, "std_accuracy": 0.03248011651156729, "total_folds": 3}, "0.819_0.181": {"average_accuracy": 0.4909560723514212, "std_accuracy": 0.0036542986107831977, "total_folds": 3}, "0.670_0.330": {"average_accuracy": 0.5917312661498708, "std_accuracy": 0.03122234101704024, "total_folds": 3}, "0.289_0.711": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.021925791664699136, "total_folds": 3}, "0.393_0.607": {"average_accuracy": 0.599483204134367, "std_accuracy": 0.05529957250396042, "total_folds": 3}, "0.152_0.848": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.04430601601933399, "total_folds": 3}, "0.534_0.466": {"average_accuracy": 0.6020671834625323, "std_accuracy": 0.04578823035315076, "total_folds": 3}, "0.690_0.310": {"average_accuracy": 0.5788113695090439, "std_accuracy": 0.03185743670785002, "total_folds": 3}, "0.651_0.349": {"average_accuracy": 0.5142118863049095, "std_accuracy": 0.04118185387728485, "total_folds": 3}, "0.162_0.838": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.027589349488453, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.035240779578774875, "total_folds": 3}, "0.520_0.480": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.044306016019333996, "total_folds": 3}, "0.419_0.581": {"average_accuracy": 0.5658914728682171, "std_accuracy": 0.041504853757645954, "total_folds": 3}, "0.549_0.451": {"average_accuracy": 0.5762273901808785, "std_accuracy": 0.019336730681002275, "total_folds": 3}, "0.772_0.228": {"average_accuracy": 0.49612403100775193, "std_accuracy": 0.05407868097275541, "total_folds": 3}, "0.866_0.134": {"average_accuracy": 0.571059431524548, "std_accuracy": 0.03485978698509575, "total_folds": 3}, "0.141_0.859": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.018988292579714593, "total_folds": 3}, "0.348_0.652": {"average_accuracy": 0.5529715762273902, "std_accuracy": 0.020346273576257894, "total_folds": 3}, "0.307_0.693": {"average_accuracy": 0.6382428940568476, "std_accuracy": 0.026351522034071182, "total_folds": 3}, "0.312_0.688": {"average_accuracy": 0.5348837209302326, "std_accuracy": 0.021925791664699136, "total_folds": 3}, "0.432_0.568": {"average_accuracy": 0.5193798449612403, "std_accuracy": 0.027589349488453027, "total_folds": 3}, "0.460_0.540": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.018988292579714593, "total_folds": 3}, "0.237_0.763": {"average_accuracy": 0.5426356589147288, "std_accuracy": 0.018988292579714548, "total_folds": 3}, "0.400_0.600": {"average_accuracy": 0.5581395348837209, "std_accuracy": 0.03797658515942914, "total_folds": 3}, "0.402_0.598": {"average_accuracy": 0.58656330749354, "std_accuracy": 0.025580090275482338, "total_folds": 3}, "0.481_0.519": {"average_accuracy": 0.5633074935400516, "std_accuracy": 0.013175761017035643, "total_folds": 3}, "0.382_0.618": {"average_accuracy": 0.5968992248062014, "std_accuracy": 0.04430601601933389, "total_folds": 3}, "0.383_0.617": {"average_accuracy": 0.5607235142118864, "std_accuracy": 0.0263515220340712, "total_folds": 3}, "0.490_0.510": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.04564217501978215, "total_folds": 3}, "0.191_0.809": {"average_accuracy": 0.5710594315245477, "std_accuracy": 0.025580090275482387, "total_folds": 3}, "0.437_0.563": {"average_accuracy": 0.5865633074935402, "std_accuracy": 0.03122234101704024, "total_folds": 3}, "0.278_0.722": {"average_accuracy": 0.5581395348837209, "std_accuracy": 0.03850042487274255, "total_folds": 3}, "0.525_0.475": {"average_accuracy": 0.5478036175710596, "std_accuracy": 0.025580090275482345, "total_folds": 3}, "0.165_0.835": {"average_accuracy": 0.5968992248062015, "std_accuracy": 0.033492199991771894, "total_folds": 3}, "0.591_0.409": {"average_accuracy": 0.5322997416020672, "std_accuracy": 0.050767138771029764, "total_folds": 3}, "0.350_0.650": {"average_accuracy": 0.58656330749354, "std_accuracy": 0.05383117998966319, "total_folds": 3}, "0.171_0.829": {"average_accuracy": 0.5684754521963825, "std_accuracy": 0.04445646132838566, "total_folds": 3}, "0.349_0.651": {"average_accuracy": 0.5529715762273901, "std_accuracy": 0.013175761017035643, "total_folds": 3}, "0.209_0.791": {"average_accuracy": 0.5917312661498708, "std_accuracy": 0.04214342746847573, "total_folds": 3}, "0.266_0.734": {"average_accuracy": 0.5633074935400518, "std_accuracy": 0.037087080347305755, "total_folds": 3}, "0.848_0.152": {"average_accuracy": 0.5581395348837209, "std_accuracy": 0.06103882072877376, "total_folds": 3}, "0.149_0.851": {"average_accuracy": 0.6072351421188631, "std_accuracy": 0.0036542986107832237, "total_folds": 3}, "0.483_0.517": {"average_accuracy": 0.5684754521963824, "std_accuracy": 0.014617194443132791, "total_folds": 3}, "0.146_0.854": {"average_accuracy": 0.58656330749354, "std_accuracy": 0.053831179989663164, "total_folds": 3}, "0.686_0.314": {"average_accuracy": 0.5581395348837209, "std_accuracy": 0.012658861719809684, "total_folds": 3}, "0.194_0.806": {"average_accuracy": 0.545219638242894, "std_accuracy": 0.057431810777500966, "total_folds": 3}, "0.305_0.695": {"average_accuracy": 0.5710594315245477, "std_accuracy": 0.020346273576257946, "total_folds": 3}, "0.547_0.453": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.010962895832349594, "total_folds": 3}, "0.140_0.860": {"average_accuracy": 0.5348837209302326, "std_accuracy": 0.006329430859904864, "total_folds": 3}, "0.620_0.380": {"average_accuracy": 0.5684754521963824, "std_accuracy": 0.013175761017035643, "total_folds": 3}, "0.808_0.192": {"average_accuracy": 0.5633074935400516, "std_accuracy": 0.050767138771029716, "total_folds": 3}, "0.491_0.509": {"average_accuracy": 0.5917312661498708, "std_accuracy": 0.07114160153972197, "total_folds": 3}, "0.254_0.746": {"average_accuracy": 0.5658914728682171, "std_accuracy": 0.0328886874970487, "total_folds": 3}, "0.562_0.438": {"average_accuracy": 0.5503875968992248, "std_accuracy": 0.021925791664699188, "total_folds": 3}, "0.366_0.634": {"average_accuracy": 0.5529715762273902, "std_accuracy": 0.031222341017040262, "total_folds": 3}, "0.752_0.248": {"average_accuracy": 0.5452196382428941, "std_accuracy": 0.023962838489652975, "total_folds": 3}, "0.225_0.775": {"average_accuracy": 0.5555555555555555, "std_accuracy": 0.020346273576257946, "total_folds": 3}, "0.648_0.352": {"average_accuracy": 0.5788113695090439, "std_accuracy": 0.026351522034071234, "total_folds": 3}, "0.500_0.500": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.06423670746592275, "total_folds": 3}, "0.424_0.576": {"average_accuracy": 0.5710594315245477, "std_accuracy": 0.031857436707849976, "total_folds": 3}, "0.318_0.682": {"average_accuracy": 0.599483204134367, "std_accuracy": 0.013175761017035608, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.638) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5747380778323389, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.22436861602014002, "momentum_ratio": 0.77563138397986, "accuracy": 0.6235754603174603}, "Clay_Set2_Mid": {"historical_ratio": 0.30691083600635227, "momentum_ratio": 0.6930891639936477, "accuracy": 0.6382500206718345}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Marginal accuracy improvement from balance optimization. Consider other factors.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.2/0.8 (accuracy: 0.624)", "  • Clay_Set2_Mid: 0.3/0.7 (accuracy: 0.638)"]}