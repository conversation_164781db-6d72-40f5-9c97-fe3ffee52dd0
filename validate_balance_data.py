#!/usr/bin/env python3
"""
Diagnostic script to validate the data being used in balance ratio optimization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem, RobustBalanceValidationConfig
import numpy as np
from collections import Counter
import json

def analyze_prediction_data():
    """Analyze the prediction data being used for balance validation"""
    
    print("🔍 BALANCE VALIDATION DATA ANALYSIS")
    print("=" * 60)
    
    # Initialize the enhanced learning system
    enhanced_system = EnhancedAdaptiveLearningSystem()
    
    # Get all predictions
    all_predictions = enhanced_system.contextual_predictions
    print(f"📊 Total predictions in system: {len(all_predictions)}")
    
    # Get learning-eligible predictions
    learning_eligible = enhanced_system.get_learning_eligible_predictions()
    print(f"🎯 Learning-eligible predictions: {len(learning_eligible)}")
    
    # Analyze filtering process
    print("\n🔬 FILTERING ANALYSIS:")
    print("-" * 30)
    
    # Check each filter criterion
    with_outcomes = [p for p in all_predictions if p.actual_winner is not None]
    print(f"   With outcomes: {len(with_outcomes)}")
    
    with_balance_ratios = [p for p in with_outcomes 
                          if p.historical_weight_used is not None and p.momentum_weight_used is not None]
    print(f"   With balance ratios: {len(with_balance_ratios)}")
    
    with_context = [p for p in with_balance_ratios 
                   if p.surface and p.set_number]
    print(f"   With context info: {len(with_context)}")
    
    completed_matches = [p for p in with_context 
                        if getattr(p, 'match_status', 'completed') == 'completed']
    print(f"   From completed matches: {len(completed_matches)}")
    
    ai_predictions = [p for p in completed_matches 
                     if getattr(p, 'is_ai_prediction', True)]
    print(f"   AI predictions only: {len(ai_predictions)}")
    
    # Analyze context segmentation
    print("\n📋 CONTEXT SEGMENTATION:")
    print("-" * 30)
    
    config = RobustBalanceValidationConfig()
    validator = enhanced_system.robust_balance_validator
    
    # Filter predictions using the same logic as validation
    filtered_predictions = validator._filter_predictions_for_validation(learning_eligible)
    print(f"   Filtered predictions: {len(filtered_predictions)}")
    
    # Segment by context
    segmented_data = validator._segment_predictions_by_context(filtered_predictions)
    
    print(f"   Number of contexts: {len(segmented_data)}")
    for context_key, context_predictions in segmented_data.items():
        status = "✅" if len(context_predictions) >= config.min_context_sample_size else "❌"
        print(f"   {status} {context_key}: {len(context_predictions)} predictions")
    
    return filtered_predictions, segmented_data

def analyze_data_quality(predictions):
    """Analyze the quality of prediction data"""
    
    print("\n🔍 DATA QUALITY ANALYSIS:")
    print("-" * 30)
    
    # Check balance ratio distributions
    hist_ratios = [p.historical_weight_used for p in predictions if p.historical_weight_used is not None]
    mom_ratios = [p.momentum_weight_used for p in predictions if p.momentum_weight_used is not None]
    
    print(f"   Historical ratios - Mean: {np.mean(hist_ratios):.3f}, Std: {np.std(hist_ratios):.3f}")
    print(f"   Momentum ratios - Mean: {np.mean(mom_ratios):.3f}, Std: {np.std(mom_ratios):.3f}")
    
    # Check if ratios sum to 1.0
    ratio_sums = [h + m for h, m in zip(hist_ratios, mom_ratios)]
    invalid_sums = [s for s in ratio_sums if abs(s - 1.0) > 0.01]
    print(f"   Invalid ratio sums (≠1.0): {len(invalid_sums)}/{len(ratio_sums)}")
    
    # Check factor data availability
    with_hist_factors = [p for p in predictions if p.historical_factors]
    with_mom_factors = [p for p in predictions if p.momentum_factors]
    
    print(f"   With historical factors: {len(with_hist_factors)}/{len(predictions)}")
    print(f"   With momentum factors: {len(with_mom_factors)}/{len(predictions)}")
    
    # Analyze factor quality
    if with_hist_factors:
        sample_hist = with_hist_factors[0].historical_factors
        print(f"   Sample historical factors: {list(sample_hist.keys())[:5]}")
    
    if with_mom_factors:
        sample_mom = with_mom_factors[0].momentum_factors
        print(f"   Sample momentum factors: {list(sample_mom.keys())[:5]}")
    
    # Check accuracy distribution
    accuracies = [1 if p.was_correct else 0 for p in predictions]
    overall_accuracy = np.mean(accuracies)
    print(f"   Overall accuracy: {overall_accuracy:.3f}")
    
    return {
        'hist_ratios': hist_ratios,
        'mom_ratios': mom_ratios,
        'overall_accuracy': overall_accuracy,
        'factor_coverage': {
            'historical': len(with_hist_factors) / len(predictions),
            'momentum': len(with_mom_factors) / len(predictions)
        }
    }

def test_simulation_logic(predictions, sample_size=10):
    """Test the simulation logic with sample data"""
    
    print("\n🧪 SIMULATION LOGIC TEST:")
    print("-" * 30)
    
    enhanced_system = EnhancedAdaptiveLearningSystem()
    validator = enhanced_system.robust_balance_validator
    
    # Test with a few sample predictions
    sample_predictions = predictions[:sample_size]
    
    print(f"   Testing with {len(sample_predictions)} sample predictions")
    
    # Test different balance ratios
    test_ratios = [(0.3, 0.7), (0.5, 0.5), (0.7, 0.3)]
    
    for hist_ratio, mom_ratio in test_ratios:
        print(f"\n   Testing ratio {hist_ratio:.1f}/{mom_ratio:.1f}:")
        
        results = []
        for pred in sample_predictions:
            # Run simulation multiple times to check consistency
            sim_results = []
            for _ in range(5):
                result = validator._simulate_prediction_with_balance(pred, hist_ratio, mom_ratio)
                sim_results.append(result)
            
            consistency = len(set(sim_results)) == 1  # All same result
            avg_result = np.mean(sim_results)
            
            results.append({
                'prediction_id': pred.prediction_id,
                'actual_correct': pred.was_correct,
                'sim_consistency': consistency,
                'sim_avg': avg_result,
                'original_hist': pred.historical_weight_used,
                'original_mom': pred.momentum_weight_used
            })
        
        # Analyze results
        consistent_count = sum(1 for r in results if r['sim_consistency'])
        avg_sim_accuracy = np.mean([r['sim_avg'] for r in results])
        
        print(f"      Consistent simulations: {consistent_count}/{len(results)}")
        print(f"      Average sim probability: {avg_sim_accuracy:.3f}")

def main():
    """Main analysis function"""
    
    try:
        # Analyze prediction data
        filtered_predictions, segmented_data = analyze_prediction_data()
        
        if not filtered_predictions:
            print("❌ No valid predictions found for analysis!")
            return
        
        # Analyze data quality
        quality_stats = analyze_data_quality(filtered_predictions)
        
        # Test simulation logic
        test_simulation_logic(filtered_predictions)
        
        # Summary
        print("\n📋 SUMMARY:")
        print("-" * 30)
        print(f"✅ Total valid predictions: {len(filtered_predictions)}")
        print(f"✅ Valid contexts: {len([k for k, v in segmented_data.items() if len(v) >= 150])}")
        print(f"✅ Overall accuracy: {quality_stats['overall_accuracy']:.3f}")
        print(f"✅ Factor coverage: Hist={quality_stats['factor_coverage']['historical']:.1%}, Mom={quality_stats['factor_coverage']['momentum']:.1%}")
        
        # Check for potential issues
        issues = []
        if quality_stats['overall_accuracy'] < 0.45 or quality_stats['overall_accuracy'] > 0.75:
            issues.append(f"Unusual overall accuracy: {quality_stats['overall_accuracy']:.3f}")
        
        if quality_stats['factor_coverage']['historical'] < 0.8:
            issues.append(f"Low historical factor coverage: {quality_stats['factor_coverage']['historical']:.1%}")
        
        if quality_stats['factor_coverage']['momentum'] < 0.8:
            issues.append(f"Low momentum factor coverage: {quality_stats['factor_coverage']['momentum']:.1%}")
        
        if issues:
            print("\n⚠️ POTENTIAL ISSUES:")
            for issue in issues:
                print(f"   • {issue}")
        else:
            print("\n✅ No major data quality issues detected!")
            
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
