{"validation_timestamp": "2025-07-29T09:36:46.889978", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.28334792874222825, "momentum_ratio": 0.7166520712577718, "accuracy": 0.6440409092970522, "confidence_interval_95": [0.6054421768707483, 0.7142857142857143], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.014695557139246813, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6099773242630385, "std_accuracy": 0.06579872167141455, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.6077097505668935, "std_accuracy": 0.021028613368470988, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5487528344671202, "std_accuracy": 0.05039934415168451, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.6122448979591836, "std_accuracy": 0.014695557139246813, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.01110879701942481, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.526077097505669, "std_accuracy": 0.04094210903688839, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.564625850340136, "std_accuracy": 0.044086671417740544, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.5691609977324262, "std_accuracy": 0.04723960692970441, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5691609977324262, "std_accuracy": 0.03940622947161625, "total_folds": 3}, "0.854_0.146": {"average_accuracy": 0.5578231292517007, "std_accuracy": 0.040053337262257856, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5736961451247167, "std_accuracy": 0.013978263045281094, "total_folds": 3}, "0.283_0.717": {"average_accuracy": 0.6439909297052154, "std_accuracy": 0.04978344308412735, "total_folds": 3}, "0.296_0.704": {"average_accuracy": 0.5850340136054423, "std_accuracy": 0.01110879701942481, "total_folds": 3}, "0.273_0.727": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.01282733389907568, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5759637188208617, "std_accuracy": 0.013978263045281094, "total_folds": 3}, "0.788_0.212": {"average_accuracy": 0.5328798185941043, "std_accuracy": 0.0533723460133325, "total_folds": 3}, "0.675_0.325": {"average_accuracy": 0.6258503401360543, "std_accuracy": 0.04338123916033784, "total_folds": 3}, "0.679_0.321": {"average_accuracy": 0.5578231292517007, "std_accuracy": 0.04338123916033784, "total_folds": 3}, "0.326_0.674": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.030591241639981934, "total_folds": 3}, "0.142_0.858": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.014695557139246864, "total_folds": 3}, "0.221_0.779": {"average_accuracy": 0.5600907029478458, "std_accuracy": 0.05279114161824401, "total_folds": 3}, "0.173_0.827": {"average_accuracy": 0.5510204081632654, "std_accuracy": 0.044435188077699354, "total_folds": 3}, "0.259_0.741": {"average_accuracy": 0.6122448979591836, "std_accuracy": 0.028861501272920285, "total_folds": 3}, "0.532_0.468": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.027956526090562237, "total_folds": 3}, "0.395_0.605": {"average_accuracy": 0.5963718820861678, "std_accuracy": 0.040942109036888406, "total_folds": 3}, "0.312_0.688": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.017854893138348746, "total_folds": 3}, "0.651_0.349": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.04842212359197871, "total_folds": 3}, "0.246_0.754": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.06626593739774353, "total_folds": 3}, "0.704_0.296": {"average_accuracy": 0.5351473922902494, "std_accuracy": 0.03254580520273767, "total_folds": 3}, "0.386_0.614": {"average_accuracy": 0.564625850340136, "std_accuracy": 0.027771992548562052, "total_folds": 3}, "0.565_0.435": {"average_accuracy": 0.5714285714285713, "std_accuracy": 0.03332639105827453, "total_folds": 3}, "0.191_0.809": {"average_accuracy": 0.6077097505668935, "std_accuracy": 0.030591241639981958, "total_folds": 3}, "0.424_0.576": {"average_accuracy": 0.6281179138321995, "std_accuracy": 0.039406229471616254, "total_folds": 3}, "0.463_0.537": {"average_accuracy": 0.5532879818594104, "std_accuracy": 0.03940622947161625, "total_folds": 3}, "0.345_0.655": {"average_accuracy": 0.562358276643991, "std_accuracy": 0.02504617010700062, "total_folds": 3}, "0.555_0.445": {"average_accuracy": 0.5419501133786847, "std_accuracy": 0.04018150826909149, "total_folds": 3}, "0.252_0.748": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.030591241639981937, "total_folds": 3}, "0.756_0.244": {"average_accuracy": 0.54421768707483, "std_accuracy": 0.0587822285569874, "total_folds": 3}, "0.446_0.554": {"average_accuracy": 0.5283446712018142, "std_accuracy": 0.01156240252515372, "total_folds": 3}, "0.241_0.759": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.014695557139246898, "total_folds": 3}, "0.631_0.369": {"average_accuracy": 0.5782312925170069, "std_accuracy": 0.027771992548562094, "total_folds": 3}, "0.324_0.676": {"average_accuracy": 0.5873015873015873, "std_accuracy": 0.03940622947161625, "total_folds": 3}, "0.622_0.378": {"average_accuracy": 0.5668934240362812, "std_accuracy": 0.08131710239020908, "total_folds": 3}, "0.369_0.631": {"average_accuracy": 0.6009070294784581, "std_accuracy": 0.03393793548094281, "total_folds": 3}, "0.498_0.502": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.055636481389502494, "total_folds": 3}, "0.732_0.268": {"average_accuracy": 0.5600907029478459, "std_accuracy": 0.017854893138348798, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.644) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.21429345433755265, "momentum_ratio": 0.7857065456624474, "accuracy": 0.6433685813953489, "confidence_interval_95": [0.6046511627906977, 0.6976744186046512], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5297157622739018, "std_accuracy": 0.03248011651156729, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.03164715429952432, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.571059431524548, "std_accuracy": 0.0430835452210157, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5452196382428941, "std_accuracy": 0.02222823066419283, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.0, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5891472868217055, "std_accuracy": 0.029005096021503425, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.025317723439619456, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5788113695090439, "std_accuracy": 0.019336730681002313, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.6434108527131782, "std_accuracy": 0.03952728305110686, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5633074935400516, "std_accuracy": 0.057431810777500966, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.033492199991771845, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5581395348837209, "std_accuracy": 0.012658861719809728, "total_folds": 3}, "0.293_0.707": {"average_accuracy": 0.58656330749354, "std_accuracy": 0.014617194443132791, "total_folds": 3}, "0.308_0.692": {"average_accuracy": 0.5529715762273902, "std_accuracy": 0.03248011651156729, "total_folds": 3}, "0.347_0.653": {"average_accuracy": 0.5478036175710596, "std_accuracy": 0.038151997571662516, "total_folds": 3}, "0.448_0.552": {"average_accuracy": 0.537467700258398, "std_accuracy": 0.038673461362004585, "total_folds": 3}, "0.252_0.748": {"average_accuracy": 0.6175710594315246, "std_accuracy": 0.05457031545721673, "total_folds": 3}, "0.226_0.774": {"average_accuracy": 0.5839793281653746, "std_accuracy": 0.045788230353150806, "total_folds": 3}, "0.238_0.762": {"average_accuracy": 0.5839793281653747, "std_accuracy": 0.018271493053915962, "total_folds": 3}, "0.648_0.352": {"average_accuracy": 0.6020671834625323, "std_accuracy": 0.02558009027548233, "total_folds": 3}, "0.242_0.758": {"average_accuracy": 0.5762273901808785, "std_accuracy": 0.07913898103934652, "total_folds": 3}, "0.529_0.471": {"average_accuracy": 0.5555555555555556, "std_accuracy": 0.06339971135082842, "total_folds": 3}, "0.746_0.254": {"average_accuracy": 0.5452196382428941, "std_accuracy": 0.03248011651156728, "total_folds": 3}, "0.311_0.689": {"average_accuracy": 0.6175710594315246, "std_accuracy": 0.0036542986107831452, "total_folds": 3}, "0.345_0.655": {"average_accuracy": 0.5452196382428941, "std_accuracy": 0.03599066738290471, "total_folds": 3}, "0.340_0.660": {"average_accuracy": 0.6175710594315245, "std_accuracy": 0.05743181077750092, "total_folds": 3}, "0.470_0.530": {"average_accuracy": 0.5658914728682171, "std_accuracy": 0.04150485375764601, "total_folds": 3}, "0.288_0.712": {"average_accuracy": 0.607235142118863, "std_accuracy": 0.02923438888626553, "total_folds": 3}, "0.276_0.724": {"average_accuracy": 0.5736434108527133, "std_accuracy": 0.027589349488452996, "total_folds": 3}, "0.271_0.729": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.018988292579714548, "total_folds": 3}, "0.262_0.738": {"average_accuracy": 0.5348837209302326, "std_accuracy": 0.027589349488453, "total_folds": 3}, "0.501_0.499": {"average_accuracy": 0.5529715762273901, "std_accuracy": 0.009668365340501157, "total_folds": 3}, "0.643_0.357": {"average_accuracy": 0.5607235142118863, "std_accuracy": 0.02635152203407122, "total_folds": 3}, "0.382_0.618": {"average_accuracy": 0.5788113695090439, "std_accuracy": 0.013175761017035643, "total_folds": 3}, "0.395_0.605": {"average_accuracy": 0.6020671834625323, "std_accuracy": 0.009668365340501158, "total_folds": 3}, "0.619_0.381": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.044306016019334024, "total_folds": 3}, "0.409_0.591": {"average_accuracy": 0.5788113695090439, "std_accuracy": 0.05743181077750101, "total_folds": 3}, "0.532_0.468": {"average_accuracy": 0.599483204134367, "std_accuracy": 0.007308597221566369, "total_folds": 3}, "0.403_0.597": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.047786155061775006, "total_folds": 3}, "0.527_0.473": {"average_accuracy": 0.5529715762273901, "std_accuracy": 0.0263515220340712, "total_folds": 3}, "0.351_0.649": {"average_accuracy": 0.5839793281653747, "std_accuracy": 0.028540984540535563, "total_folds": 3}, "0.629_0.371": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.04564217501978215, "total_folds": 3}, "0.350_0.650": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.03164715429952428, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.643) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5762174292628335, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.28334792874222825, "momentum_ratio": 0.7166520712577718, "accuracy": 0.6440409092970522}, "Clay_Set2_Mid": {"historical_ratio": 0.21429345433755265, "momentum_ratio": 0.7857065456624474, "accuracy": 0.6433685813953489}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Marginal accuracy improvement from balance optimization. Consider other factors.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.3/0.7 (accuracy: 0.644)", "  • Clay_Set2_Mid: 0.2/0.8 (accuracy: 0.643)"]}