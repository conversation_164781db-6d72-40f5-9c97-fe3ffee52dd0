{"validation_timestamp": "2025-07-28T23:53:06.300350", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 1.0, "confidence_interval_95": [1.0, 1.0], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.07725787141807247, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.8106060606060606, "std_accuracy": 0.15782323224242165, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.8106060606060606, "std_accuracy": 0.15782323224242165, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.962121212121212, "std_accuracy": 0.05356869554443543, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.962121212121212, "std_accuracy": 0.05356869554443543, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (1.000) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 1.0, "confidence_interval_95": [1.0, 1.0], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.49166666666666664, "std_accuracy": 0.07728015412913086, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5416666666666666, "std_accuracy": 0.06561673228343173, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.9583333333333334, "std_accuracy": 0.05892556509887896, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (1.000) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.8657467532467532, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.5, "momentum_ratio": 0.5, "accuracy": 1.0}, "Clay_Set2_Mid": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 1.0}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.5/0.5 (accuracy: 1.000)", "  • Clay_Set2_Mid: 0.4/0.6 (accuracy: 1.000)"]}