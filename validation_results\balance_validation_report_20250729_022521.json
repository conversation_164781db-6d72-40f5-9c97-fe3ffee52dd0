{"validation_timestamp": "2025-07-29T02:25:21.966511", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6734812789115651, "confidence_interval_95": [0.6326530612244898, 0.7346938775510204], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6598639455782312, "std_accuracy": 0.05851921950369136, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.03332639105827457, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.044086671417740565, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6462585034013606, "std_accuracy": 0.025453451610707063, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.673469387755102, "std_accuracy": 0.04408667141774056, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.07513851032100176, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5986394557823129, "std_accuracy": 0.05090690322141413, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.673) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6348934523809525, "confidence_interval_95": [0.5714285714285714, 0.6904761904761904], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 126}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5634920634920634, "std_accuracy": 0.1294405272246039, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.04489566864676491, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5793650793650794, "std_accuracy": 0.09589719026662359, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.04489566864676491, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.029695693545824964, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.634920634920635, "std_accuracy": 0.04892392065848395, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5793650793650794, "std_accuracy": 0.07856742013183861, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.635) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6061710398445092, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6734812789115651}, "Clay_Set2_Mid": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6348934523809525}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.6/0.4 (accuracy: 0.673)", "  • Clay_Set2_Mid: 0.7/0.3 (accuracy: 0.635)"]}