{"validation_timestamp": "2025-07-29T00:18:27.688840", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6976969696969697, "confidence_interval_95": [0.6363636363636364, 0.8181818181818182], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5757575757575758, "std_accuracy": 0.02834588929374199, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5757575757575758, "std_accuracy": 0.07499617376220956, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6136363636363636, "std_accuracy": 0.04909652044248374, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.010713739108887073, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6742424242424243, "std_accuracy": 0.10220255729721246, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5530303030303031, "std_accuracy": 0.08367697740293378, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6969696969696969, "std_accuracy": 0.0857099128710967, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.698) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.64175, "confidence_interval_95": [0.6, 0.6750000000000002], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6416666666666666, "std_accuracy": 0.051370116691408146, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6416666666666667, "std_accuracy": 0.031180478223116207, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5166666666666667, "std_accuracy": 0.011785113019775802, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6, "std_accuracy": 0.07071067811865471, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5916666666666667, "std_accuracy": 0.04714045207910316, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5166666666666666, "std_accuracy": 0.09204467514322717, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.625, "std_accuracy": 0.02041241452319317, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.642) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6031385281385282, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6976969696969697}, "Clay_Set2_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.64175}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.8/0.2 (accuracy: 0.698)", "  • Clay_Set2_Mid: 0.3/0.7 (accuracy: 0.642)"]}