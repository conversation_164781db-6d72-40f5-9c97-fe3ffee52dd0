{"validation_timestamp": "2025-07-29T00:34:21.784125", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6590130000000002, "confidence_interval_95": [0.5454545454545454, 0.75], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5, "std_accuracy": 0.08088695645478267, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5757575757575758, "std_accuracy": 0.06516913081092898, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6060606060606061, "std_accuracy": 0.059651574803119785, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5303030303030304, "std_accuracy": 0.028345889293742006, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6590909090909091, "std_accuracy": 0.08503766788122595, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5909090909090909, "std_accuracy": 0.0808869564547827, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5984848484848485, "std_accuracy": 0.028345889293741953, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.659) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6583217833333332, "confidence_interval_95": [0.55, 0.8000000000000002], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6083333333333333, "std_accuracy": 0.031180478223116207, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6083333333333334, "std_accuracy": 0.031180478223116204, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.625, "std_accuracy": 0.03535533905932741, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6333333333333333, "std_accuracy": 0.062360956446232324, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6166666666666666, "std_accuracy": 0.042491829279939906, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6583333333333333, "std_accuracy": 0.10474837574980446, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.65, "std_accuracy": 0.02041241452319317, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.658) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6043290043290044, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6590130000000002}, "Clay_Set2_Mid": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6583217833333332}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.6/0.4 (accuracy: 0.659)", "  • Clay_Set2_Mid: 0.7/0.3 (accuracy: 0.658)"]}