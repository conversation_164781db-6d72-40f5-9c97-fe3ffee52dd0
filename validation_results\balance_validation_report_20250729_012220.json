{"validation_timestamp": "2025-07-29T01:22:20.909582", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6590908181818182, "confidence_interval_95": [0.5681818181818182, 0.75], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.13927860841553213, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6590909090909091, "std_accuracy": 0.07422696190252052, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6212121212121212, "std_accuracy": 0.07499617376220957, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5303030303030303, "std_accuracy": 0.010713739108887075, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6439393939393939, "std_accuracy": 0.038628935709036215, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5909090909090909, "std_accuracy": 0.11134044285378084, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5454545454545454, "std_accuracy": 0.08503766788122594, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.659) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6333103666666671, "confidence_interval_95": [0.6, 0.65], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5416666666666666, "std_accuracy": 0.07728015412913089, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.575, "std_accuracy": 0.1136515141415488, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6333333333333333, "std_accuracy": 0.023570226039551608, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5666666666666667, "std_accuracy": 0.0716860438920219, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6, "std_accuracy": 0.04082482904638629, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.05137011669140814, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6166666666666667, "std_accuracy": 0.11242281302693369, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.633) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.59491341991342, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6590908181818182}, "Clay_Set2_Mid": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6333103666666671}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.3/0.7 (accuracy: 0.659)", "  • Clay_Set2_Mid: 0.4/0.6 (accuracy: 0.633)"]}