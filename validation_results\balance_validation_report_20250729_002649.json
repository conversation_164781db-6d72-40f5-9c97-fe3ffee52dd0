{"validation_timestamp": "2025-07-29T00:26:49.642127", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 100000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6666617424242424, "confidence_interval_95": [0.6363636363636364, 0.7045454545454546], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6590909090909092, "std_accuracy": 0.03711348095126028, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.028345889293742006, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5984848484848485, "std_accuracy": 0.07499617376220956, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6363636363636364, "std_accuracy": 0.04909652044248374, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6136363636363638, "std_accuracy": 0.06428243465332249, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5606060606060607, "std_accuracy": 0.09522579613618586, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5606060606060607, "std_accuracy": 0.04670010608309831, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.667) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6500568333333333, "confidence_interval_95": [0.6, 0.6999999999999998], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6416666666666666, "std_accuracy": 0.031180478223116204, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6083333333333333, "std_accuracy": 0.031180478223116207, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5666666666666667, "std_accuracy": 0.05137011669140813, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6250000000000001, "std_accuracy": 0.05400617248673217, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6333333333333333, "std_accuracy": 0.04249182927993991, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6499999999999999, "std_accuracy": 0.04082482904638629, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6333333333333333, "std_accuracy": 0.062360956446232324, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.650) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6181277056277056, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6666617424242424}, "Clay_Set2_Mid": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6500568333333333}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.3/0.7 (accuracy: 0.667)", "  • Clay_Set2_Mid: 0.7/0.3 (accuracy: 0.650)"]}