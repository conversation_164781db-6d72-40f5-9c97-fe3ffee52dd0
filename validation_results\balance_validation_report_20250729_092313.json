{"validation_timestamp": "2025-07-29T09:23:13.126187", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.7237528002182156, "momentum_ratio": 0.27624719978178436, "accuracy": 0.655322410430839, "confidence_interval_95": [0.6258503401360545, 0.6734693877551021], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.6031746031746033, "std_accuracy": 0.05700591877540375, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6031746031746031, "std_accuracy": 0.027956526090562192, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.6553287981859411, "std_accuracy": 0.021028613368470988, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5895691609977325, "std_accuracy": 0.04094210903688842, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.6190476190476191, "std_accuracy": 0.01110879701942481, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.04546471123755874, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.02002666863112888, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.048422123591978686, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.6213151927437642, "std_accuracy": 0.02312480505030739, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5827664399092971, "std_accuracy": 0.03698300777845825, "total_folds": 3}, "0.719_0.281": {"average_accuracy": 0.5963718820861678, "std_accuracy": 0.035709786276697526, "total_folds": 3}, "0.728_0.272": {"average_accuracy": 0.5941043083900227, "std_accuracy": 0.05394728913093116, "total_folds": 3}, "0.226_0.774": {"average_accuracy": 0.619047619047619, "std_accuracy": 0.014695557139246813, "total_folds": 3}, "0.203_0.797": {"average_accuracy": 0.5736961451247167, "std_accuracy": 0.022447834323382477, "total_folds": 3}, "0.444_0.556": {"average_accuracy": 0.6530612244897959, "std_accuracy": 0.027771992548562094, "total_folds": 3}, "0.439_0.561": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.04936857382557198, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5918367346938774, "std_accuracy": 0.014695557139246813, "total_folds": 3}, "0.893_0.107": {"average_accuracy": 0.5759637188208616, "std_accuracy": 0.03698300777845822, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5895691609977324, "std_accuracy": 0.02850295938770189, "total_folds": 3}, "0.705_0.295": {"average_accuracy": 0.6054421768707482, "std_accuracy": 0.020026668631128876, "total_folds": 3}, "0.678_0.322": {"average_accuracy": 0.6190476190476191, "std_accuracy": 0.04546471123755875, "total_folds": 3}, "0.664_0.336": {"average_accuracy": 0.63718820861678, "std_accuracy": 0.052498126543266334, "total_folds": 3}, "0.657_0.343": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.02545345161070716, "total_folds": 3}, "0.689_0.311": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.09636521252097857, "total_folds": 3}, "0.811_0.189": {"average_accuracy": 0.5804988662131519, "std_accuracy": 0.021028613368470988, "total_folds": 3}, "0.303_0.697": {"average_accuracy": 0.5736961451247166, "std_accuracy": 0.055636481389502446, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.655) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.456666202282873, "momentum_ratio": 0.543333797717127, "accuracy": 0.6511634547803619, "confidence_interval_95": [0.6434108527131784, 0.6589147286821705], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5503875968992249, "std_accuracy": 0.016746099995885923, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6149870801033592, "std_accuracy": 0.04834182670250571, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.5839793281653748, "std_accuracy": 0.03599066738290471, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.6459948320413437, "std_accuracy": 0.023962838489652975, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.6511627906976744, "std_accuracy": 0.006329430859904774, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5839793281653747, "std_accuracy": 0.02034627357625784, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.02192579166469916, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5426356589147286, "std_accuracy": 0.03952728305110688, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.6020671834625323, "std_accuracy": 0.10290598321312597, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.02531772343961937, "total_folds": 3}, "0.451_0.549": {"average_accuracy": 0.6175710594315246, "std_accuracy": 0.023962838489652975, "total_folds": 3}, "0.572_0.428": {"average_accuracy": 0.5762273901808785, "std_accuracy": 0.03485978698509567, "total_folds": 3}, "0.445_0.555": {"average_accuracy": 0.6124031007751938, "std_accuracy": 0.038500424872742495, "total_folds": 3}, "0.586_0.414": {"average_accuracy": 0.6201550387596898, "std_accuracy": 0.06962373945895337, "total_folds": 3}, "0.595_0.405": {"average_accuracy": 0.6175710594315246, "std_accuracy": 0.013175761017035608, "total_folds": 3}, "0.429_0.571": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.03952728305110688, "total_folds": 3}, "0.265_0.735": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.02282108750989108, "total_folds": 3}, "0.287_0.713": {"average_accuracy": 0.6227390180878553, "std_accuracy": 0.018271493053915885, "total_folds": 3}, "0.307_0.693": {"average_accuracy": 0.6304909560723514, "std_accuracy": 0.009668365340501157, "total_folds": 3}, "0.321_0.679": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.006329430859904955, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.6098191214470284, "std_accuracy": 0.01933673068100227, "total_folds": 3}, "0.858_0.142": {"average_accuracy": 0.5891472868217055, "std_accuracy": 0.038500424872742495, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.599483204134367, "std_accuracy": 0.03122234101704019, "total_folds": 3}, "0.522_0.478": {"average_accuracy": 0.6098191214470284, "std_accuracy": 0.013175761017035631, "total_folds": 3}, "0.805_0.195": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.037976585159429095, "total_folds": 3}, "0.665_0.335": {"average_accuracy": 0.5943152454780362, "std_accuracy": 0.031857436707850066, "total_folds": 3}, "0.297_0.703": {"average_accuracy": 0.627906976744186, "std_accuracy": 0.05481447916174789, "total_folds": 3}, "0.136_0.864": {"average_accuracy": 0.6304909560723514, "std_accuracy": 0.026351522034071213, "total_folds": 3}, "0.150_0.850": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.05023829998765783, "total_folds": 3}, "0.497_0.503": {"average_accuracy": 0.6201550387596898, "std_accuracy": 0.054814479161747864, "total_folds": 3}, "0.545_0.455": {"average_accuracy": 0.5762273901808785, "std_accuracy": 0.009668365340501157, "total_folds": 3}, "0.782_0.218": {"average_accuracy": 0.6072351421188631, "std_accuracy": 0.03122234101704024, "total_folds": 3}, "0.827_0.173": {"average_accuracy": 0.5891472868217055, "std_accuracy": 0.016746099995885923, "total_folds": 3}, "0.279_0.721": {"average_accuracy": 0.5994832041343668, "std_accuracy": 0.0073085972215664215, "total_folds": 3}, "0.121_0.879": {"average_accuracy": 0.5917312661498709, "std_accuracy": 0.04069254715251582, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.651) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6020438420634489, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.7237528002182156, "momentum_ratio": 0.27624719978178436, "accuracy": 0.655322410430839}, "Clay_Set2_Mid": {"historical_ratio": 0.456666202282873, "momentum_ratio": 0.543333797717127, "accuracy": 0.6511634547803619}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.7/0.3 (accuracy: 0.655)", "  • Clay_Set2_Mid: 0.5/0.5 (accuracy: 0.651)"]}