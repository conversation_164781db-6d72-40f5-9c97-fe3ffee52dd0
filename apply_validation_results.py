#!/usr/bin/env python3
"""
Apply the validated balance ratios from the 1,000,000 bootstrap validation
"""

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem

def main():
    print("🔧 Applying Validated Balance Ratios...")
    print("=" * 60)

    # Create enhanced learning system instance
    enhanced_system = EnhancedAdaptiveLearningSystem()

    # First, run validation to get the latest results
    print("📊 Running validation to get latest results...")
    validation_results = enhanced_system.run_robust_balance_validation()
    
    if validation_results.get('status') == 'success' or 'context_results' in validation_results:
        print("✅ Validation completed successfully!")

        # Check if new results are better than current performance
        context_results = validation_results.get('context_results', {})
        should_apply = True

        # Store previous best results for comparison
        previous_best = {
            'Clay_Set1_Mid': 0.720,  # From your 1M bootstrap run
            'Clay_Set2_Mid': 0.692   # From your 1M bootstrap run
        }

        print("\n🔍 Checking if new results improve performance...")
        for context, data in context_results.items():
            optimal_balance = data.get('optimal_balance', {})
            new_accuracy = optimal_balance.get('accuracy', 0.0)
            previous_accuracy = previous_best.get(context, 0.60)  # Default to 60% if unknown

            print(f"📊 {context}:")
            print(f"   Previous: {previous_accuracy:.3f}")
            print(f"   New:      {new_accuracy:.3f}")
            print(f"   Change:   {new_accuracy - previous_accuracy:+.3f}")

            # Only apply if new result is better OR within 1% of previous (to account for noise)
            improvement_threshold = -0.01  # Allow 1% degradation for noise tolerance

            if (new_accuracy - previous_accuracy) < improvement_threshold:
                print(f"❌ {context}: New result is significantly worse ({new_accuracy:.3f} vs {previous_accuracy:.3f})")
                should_apply = False
            else:
                print(f"✅ {context}: New result is acceptable")

        if should_apply:
            # Apply the validated ratios
            print("\n🔧 Applying validated balance ratios...")
            application_results = enhanced_system.apply_validated_balance_ratios(validation_results)
        else:
            print("\n❌ Skipping application - new results are worse than previous performance")
            print("💡 Suggestion: Try different bootstrap settings or collect more data")
            application_results = {'status': 'rejected', 'message': 'Results worse than previous performance'}
        
        if application_results.get('status') == 'success':
            print(f"🎉 Successfully applied {application_results['total_changes']} balance ratio changes!")
            print(f"📝 New configuration version: {application_results['new_version']}")
            
            # Show what was applied
            changes = application_results.get('changes_applied', {})
            print("\n📋 Applied Changes:")
            for context, ratio in changes.items():
                print(f"   {context}: {ratio}")
                
        elif application_results.get('status') == 'no_changes':
            print("ℹ️ No changes applied - no statistically significant improvements found")

        elif application_results.get('status') == 'rejected':
            print(f"🛡️ Changes rejected: {application_results.get('message', 'Below quality threshold')}")

        else:
            print(f"❌ Failed to apply changes: {application_results.get('message', 'Unknown error')}")
            
    else:
        print(f"❌ Validation failed: {validation_results.get('message', 'Unknown error')}")

    print("\n" + "=" * 60)
    print("✅ Application process complete!")

if __name__ == "__main__":
    main()
