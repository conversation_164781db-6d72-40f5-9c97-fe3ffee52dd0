{"validation_timestamp": "2025-07-29T09:44:41.417714", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.7372343894881864, "momentum_ratio": 0.2627656105118136, "accuracy": 1.0, "confidence_interval_95": [1.0, 1.0], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (1.000) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.7372343894881864, "momentum_ratio": 0.2627656105118136, "accuracy": 1.0, "confidence_interval_95": [1.0, 1.0], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 1.0, "std_accuracy": 0.0, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (1.000) with statistical significance"}}, "overall_summary": {"overall_accuracy": 1.0, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.7372343894881864, "momentum_ratio": 0.2627656105118136, "accuracy": 1.0}, "Clay_Set2_Mid": {"historical_ratio": 0.7372343894881864, "momentum_ratio": 0.2627656105118136, "accuracy": 1.0}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.7/0.3 (accuracy: 1.000)", "  • Clay_Set2_Mid: 0.7/0.3 (accuracy: 1.000)"]}