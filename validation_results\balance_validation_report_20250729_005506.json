{"validation_timestamp": "2025-07-29T00:55:06.835685", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6136479696969697, "confidence_interval_95": [0.5681818181818182, 0.6590909090909091], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5757575757575758, "std_accuracy": 0.04670010608309831, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5378787878787878, "std_accuracy": 0.08367697740293378, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6136363636363636, "std_accuracy": 0.03711348095126024, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5227272727272728, "std_accuracy": 0.04909652044248381, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.537878787878788, "std_accuracy": 0.04285495643554835, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6060606060606061, "std_accuracy": 0.05356869554443537, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.537878787878788, "std_accuracy": 0.028345889293741995, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.614) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6916271666666672, "confidence_interval_95": [0.65, 0.775], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6166666666666666, "std_accuracy": 0.05892556509887896, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6499999999999999, "std_accuracy": 0.04082482904638629, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5750000000000001, "std_accuracy": 0.07359800721939874, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6, "std_accuracy": 0.035355339059327355, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6, "std_accuracy": 0.05400617248673218, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6916666666666668, "std_accuracy": 0.05892556509887896, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6583333333333333, "std_accuracy": 0.08249579113843056, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.692) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.594534632034632, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.4, "momentum_ratio": 0.6, "accuracy": 0.6136479696969697}, "Clay_Set2_Mid": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6916271666666672}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.4/0.6 (accuracy: 0.614)", "  • Clay_Set2_Mid: 0.7/0.3 (accuracy: 0.692)"]}