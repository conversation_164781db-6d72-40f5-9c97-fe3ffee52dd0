{"validation_timestamp": "2025-07-29T00:25:40.418894", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 10000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6671772727272728, "confidence_interval_95": [0.5909090909090909, 0.75], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 132}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5454545454545455, "std_accuracy": 0.032141217326661274, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6515151515151515, "std_accuracy": 0.04670010608309831, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5909090909090909, "std_accuracy": 0.06690727929036247, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5530303030303031, "std_accuracy": 0.07025468557193716, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.06516913081092898, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6212121212121211, "std_accuracy": 0.05669177858748396, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5984848484848485, "std_accuracy": 0.0428549564355483, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.667) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6416925000000001, "confidence_interval_95": [0.6, 0.6750000000000002], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 120}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5916666666666667, "std_accuracy": 0.0716860438920219, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.575, "std_accuracy": 0.020412414523193124, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5083333333333333, "std_accuracy": 0.042491829279939886, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5833333333333334, "std_accuracy": 0.062360956446232366, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6416666666666667, "std_accuracy": 0.031180478223116207, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5666666666666668, "std_accuracy": 0.08498365855987976, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6, "std_accuracy": 0.03535533905932741, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.642) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5924242424242424, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6671772727272728}, "Clay_Set2_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6416925000000001}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.6/0.4 (accuracy: 0.667)", "  • Clay_Set2_Mid: 0.6/0.4 (accuracy: 0.642)"]}